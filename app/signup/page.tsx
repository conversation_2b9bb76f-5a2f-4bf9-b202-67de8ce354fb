import Link from "next/link";
import { Metada<PERSON> } from "next";
import { ArrowLeft } from "lucide-react";

import SignUpForm from "@/components/auth/signup-form";

export const metadata: Metadata = {
  title: "Sign Up | TaskMaster",
  description: "Create a new TaskMaster account",
};

export default function SignupPage() {
  return (
    <div className="container relative flex flex-col items-center justify-center min-h-screen">
      <Link
        href="/"
        className="absolute left-4 top-4 md:left-8 md:top-8 flex items-center text-sm font-medium text-muted-foreground hover:text-primary"
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back
      </Link>
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
        <div className="flex flex-col space-y-2 text-center">
          <h1 className="text-2xl font-semibold tracking-tight">Create an account</h1>
          <p className="text-sm text-muted-foreground">
            Enter your details to create a new account
          </p>
        </div>
        <SignUpForm />
      </div>
    </div>
  );
}