import { Metadata } from "next";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs";

import TaskForm from "@/components/tasks/task-form";

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: "Edit Task | TaskMaster",
  description: "Edit an existing task",
};

export default async function EditTaskPage({ params }: { params: { id: string } }) {
  const cookieStore = cookies();
  const supabase = createServerComponentClient({ cookies: () => cookieStore });

  const { data: { user } } = await supabase.auth.getUser();
  
  if (!user) {
    redirect("/login");
  }

  const { data: task, error } = await supabase
    .from('tasks')
    .select('*')
    .eq('id', params.id)
    .single();

  if (error || !task) {
    redirect("/dashboard");
  }

  return (
    <div className="container max-w-4xl py-10">
      <h1 className="text-2xl font-bold mb-6">Edit Task</h1>
      <TaskForm task={task} isEditing={true} />
    </div>
  );
}