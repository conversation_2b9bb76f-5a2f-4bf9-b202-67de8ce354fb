import { Metadata } from "next";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs";

import TaskList from "@/components/tasks/task-list";

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: "Dashboard | TaskMaster",
  description: "Manage your tasks",
};

export default async function DashboardPage() {
  const cookieStore = cookies();
  const supabase = createServerComponentClient({ cookies: () => cookieStore });

  const { data: { user } } = await supabase.auth.getUser();
  
  if (!user) {
    redirect("/login");
  }

  return (
    <div className="container py-10">
      <TaskList />
    </div>
  );
}